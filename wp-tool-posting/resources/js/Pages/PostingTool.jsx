import React, { useState } from 'react';
import route from 'ziggy-js';
import { Head } from '@inertiajs/react';
import AuthenticatedLayout from '../Layouts/AuthenticatedLayout';
import PrimaryButton from '../Components/PrimaryButton';
import SecondaryButton from '../Components/SecondaryButton';

// Progress Bar Component
const ProgressBar = ({ current, total, label }) => {
    const percentage = total > 0 ? (current / total) * 100 : 0;

    return (
        <div className="w-full">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>{label}</span>
                <span>{current}/{total}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
                    style={{ width: `${percentage}%` }}
                ></div>
            </div>
        </div>
    );
};

// Loading Spinner Component
const LoadingSpinner = () => (
    <div className="flex items-center justify-center">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
    </div>
);

// Success/Error Toast Component
const Toast = ({ type, message, onClose }) => {
    const bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500';

    return (
        <div className={`fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 flex items-center space-x-2`}>
            <span>{message}</span>
            <button onClick={onClose} className="ml-2 text-white hover:text-gray-200">
                ×
            </button>
        </div>
    );
};

export default function PostingTool({ auth, categories = [], authors = [], customTaxonomies = {} }) {
    const [urlsInput, setUrlsInput] = useState('');
    const [jobs, setJobs] = useState([]);
    const [loading, setLoading] = useState(false);
    const [fetchProgress, setFetchProgress] = useState({ current: 0, total: 0 });
    const [postProgress, setPostProgress] = useState({ current: 0, total: 0 });
    const [toast, setToast] = useState(null);

    // Function to parse AI response and extract clean title and content
    const parseAIResponse = (title, content) => {
        let cleanTitle = title || '';
        let cleanContent = content || '';

        // Clean up title - remove asterisks and extra formatting
        cleanTitle = cleanTitle.replace(/^\*+\s*/, '').replace(/\s*\*+$/, '').trim();

        // If title is empty or just asterisks, try to extract from content
        if (!cleanTitle || cleanTitle === '**') {
            const titleMatch = cleanContent.match(/Title:\s*\n\s*(.+?)(?:\n\s*Content:|$)/is);
            if (titleMatch) {
                cleanTitle = titleMatch[1].trim();
            }
        }

        // Clean up content - remove title/content markers
        cleanContent = cleanContent.replace(/^\*\*Title:\*\*.*?\n/i, '');
        cleanContent = cleanContent.replace(/Title:\s*\n\s*.+?\n/i, '');
        cleanContent = cleanContent.replace(/^\*\*Content:\*\*\s*\n?/i, '');
        cleanContent = cleanContent.replace(/^Content:\s*\n?/i, '');

        // Remove URLs at the end of content (like the automattic.com URL)
        cleanContent = cleanContent.replace(/\([^)]*https?:\/\/[^)]*\)\s*$/i, '');
        cleanContent = cleanContent.replace(/https?:\/\/[^\s]+\s*$/i, '');

        // More aggressive removal of "Content:" at the beginning (handle multiple formats)
        cleanContent = cleanContent.replace(/^Content:\s*/gmi, '');
        cleanContent = cleanContent.replace(/^\*\*Content:\*\*\s*/gmi, '');

        // Remove any line that only contains "Content:" or similar
        cleanContent = cleanContent.replace(/^Content:\s*$/gmi, '');

        cleanContent = cleanContent.trim();

        return { cleanTitle, cleanContent };
    };

    // Helper function to show toast notifications
    const showToast = (type, message) => {
        setToast({ type, message });
        setTimeout(() => setToast(null), 5000); // Auto-hide after 5 seconds
    };

    const fetchJobs = async () => {
        const urls = urlsInput
            .split('\n')
            .map((u) => u.trim())
            .filter(Boolean);
        if (urls.length === 0) {
            showToast('error', 'Please enter at least one URL');
            return;
        }

        setLoading(true);
        setFetchProgress({ current: 0, total: urls.length });

        try {
            showToast('success', `Starting to fetch ${urls.length} job${urls.length > 1 ? 's' : ''}...`);

            const res = await window.axios.post(route('posting-tool.fetch'), { urls });

            setJobs(
                res.data.jobs.map((job) => {
                    // Parse the AI response to get clean title and content
                    const { cleanTitle, cleanContent } = parseAIResponse(job.title, job.description);

                    return {
                        ...job,
                        title: cleanTitle,
                        category_ids: [],
                        content: cleanContent,
                        excerpt: job.excerpt || '',
                        new_application_link: job.url || '',
                        job_type: job.job_type || [], // Use job_type from backend if available
                        skill: [],
                        preferred_location: [],
                        timezone: [],
                        salary: [],
                        date: null,
                        author_id: job.author_id || null, // Preserve auto-selected author_id from backend
                    };
                }),
            );

            setFetchProgress({ current: urls.length, total: urls.length });
            showToast('success', `Successfully fetched ${res.data.jobs.length} job${res.data.jobs.length > 1 ? 's' : ''}!`);

        } catch (e) {
            console.error(e);
            showToast('error', 'Failed to fetch jobs. Please try again.');
        } finally {
            setLoading(false);
            setTimeout(() => setFetchProgress({ current: 0, total: 0 }), 2000);
        }
    };

    const postJobs = async () => {
        if (jobs.length === 0) {
            showToast('error', 'No jobs to post');
            return;
        }

        setLoading(true);
        setPostProgress({ current: 0, total: jobs.length });

        try {
            showToast('success', `Creating ${jobs.length} draft post${jobs.length > 1 ? 's' : ''}...`);
            console.log('Sending jobs to WordPress:', jobs);

            const response = await window.axios.post(route('posting-tool.post'), { jobs });
            console.log('WordPress response:', response.data);

            const { successful_count, failed_count, message } = response.data;

            setPostProgress({ current: jobs.length, total: jobs.length });

            if (failed_count > 0) {
                showToast('error', `${message}\nSuccessful: ${successful_count}, Failed: ${failed_count}`);
            } else {
                showToast('success', message);
            }
        } catch (e) {
            console.error('Error posting jobs:', e);
            showToast('error', 'Failed to post jobs: ' + (e.response?.data?.message || e.message));
        } finally {
            setLoading(false);
            setTimeout(() => setPostProgress({ current: 0, total: 0 }), 2000);
        }
    };

    const toggleCategory = (jobIndex, categoryId) => {
        setJobs((prev) => {
            const updated = [...prev];
            const ids = new Set(updated[jobIndex].category_ids);
            if (ids.has(categoryId)) ids.delete(categoryId);
            else ids.add(categoryId);
            updated[jobIndex].category_ids = Array.from(ids);
            return updated;
        });
    };
    
    const toggleTaxonomy = (jobIndex, taxonomy, termId) => {
        setJobs((prev) => {
            const updated = [...prev];
            const ids = new Set(updated[jobIndex][taxonomy] || []);
            if (ids.has(termId)) ids.delete(termId);
            else ids.add(termId);
            updated[jobIndex][taxonomy] = Array.from(ids);
            return updated;
        });
    };

    const updateJobField = (index, field, value) => {
        setJobs((prev) => {
            const updated = [...prev];
            updated[index] = { ...updated[index], [field]: value };
            return updated;
        });
    };

    return (
        <AuthenticatedLayout user={auth.user}>
            <Head title="WordPress Posting Tool" />
            <div className="min-h-screen bg-gray-50">
                <div className="max-w-7xl mx-auto p-6 lg:p-8">
                    {/* Header */}
                    <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">WordPress Posting Tool</h1>
                        <p className="text-gray-600">Fetch job listings and create WordPress draft posts with ease</p>
                    </div>

                    {/* Progress Bars */}
                    {(fetchProgress.total > 0 || postProgress.total > 0) && (
                        <div className="bg-white rounded-lg shadow-sm p-6 mb-6 space-y-4">
                            {fetchProgress.total > 0 && (
                                <ProgressBar
                                    current={fetchProgress.current}
                                    total={fetchProgress.total}
                                    label="Fetching Jobs"
                                />
                            )}
                            {postProgress.total > 0 && (
                                <ProgressBar
                                    current={postProgress.current}
                                    total={postProgress.total}
                                    label="Creating Posts"
                                />
                            )}
                        </div>
                    )}

                    {/* URL Input Section */}
                    <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
                        <h2 className="text-xl font-semibold text-gray-900 mb-4">📋 Job URLs</h2>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Enter job URLs (one per line)
                        </label>
                        <textarea
                            value={urlsInput}
                            onChange={(e) => setUrlsInput(e.target.value)}
                            className="w-full border border-gray-300 rounded-lg p-3 h-32 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                            placeholder="https://example.com/job1&#10;https://example.com/job2&#10;https://example.com/job3"
                        />
                        <div className="mt-4">
                            <PrimaryButton
                                onClick={fetchJobs}
                                disabled={loading}
                                className="bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                            >
                                {loading ? (
                                    <>
                                        <LoadingSpinner />
                                        <span>Fetching Jobs...</span>
                                    </>
                                ) : (
                                    <>
                                        <span>🚀</span>
                                        <span>Fetch Jobs</span>
                                    </>
                                )}
                            </PrimaryButton>
                        </div>
                    </div>

                    {/* Jobs Preview */}
                    {jobs.length > 0 && (
                        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
                            <h2 className="text-xl font-semibold text-gray-900 mb-4">
                                📝 Job Preview ({jobs.length} job{jobs.length > 1 ? 's' : ''})
                            </h2>
                            <div className="space-y-6">
                                {jobs.map((job, idx) => (
                                    <div key={idx} className="border border-gray-200 rounded-lg p-6 bg-gray-50 hover:bg-gray-100 transition-colors">
                                        <div className="text-sm text-blue-600 mb-3 font-medium">🔗 {job.url}</div>

                                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                            <div className="space-y-4">
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-2">📝 Title</label>
                                                    <input
                                                        type="text"
                                                        value={job.title}
                                                        onChange={(e) => updateJobField(idx, 'title', e.target.value)}
                                                        className="w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                                        placeholder="Job title"
                                                    />
                                                </div>

                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-2">📄 Content</label>
                                                    <textarea
                                                        value={job.content}
                                                        onChange={(e) => updateJobField(idx, 'content', e.target.value)}
                                                        className="w-full border border-gray-300 rounded-lg p-3 h-32 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                                        placeholder="Job description content"
                                                    />
                                                </div>

                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-2">✂️ Excerpt (optional)</label>
                                                    <textarea
                                                        value={job.excerpt}
                                                        onChange={(e) => updateJobField(idx, 'excerpt', e.target.value)}
                                                        placeholder="Enter a short excerpt for this post"
                                                        className="w-full border border-gray-300 rounded-lg p-3 h-20 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                                    />
                                                </div>

                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-2">🔗 Application Link</label>
                                                    <input
                                                        type="text"
                                                        value={job.new_application_link}
                                                        onChange={(e) => updateJobField(idx, 'new_application_link', e.target.value)}
                                                        placeholder="URL for job application"
                                                        className="w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                                    />
                                                </div>
                                            </div>

                                            <div className="space-y-4">
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-2">👤 Author (optional)</label>
                                                    <select
                                                        value={job.author_id || ''}
                                                        onChange={(e) => updateJobField(idx, 'author_id', e.target.value ? parseInt(e.target.value) : null)}
                                                        className="w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                                    >
                                                        <option value="">Select Author</option>
                                                        {authors.map((author) => (
                                                            <option key={author.id} value={author.id}>
                                                                {author.name}
                                                            </option>
                                                        ))}
                                                    </select>
                                                </div>

                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-2">📅 Publish Date (optional)</label>
                                                    <input
                                                        type="datetime-local"
                                                        value={job.date || ''}
                                                        onChange={(e) => updateJobField(idx, 'date', e.target.value)}
                                                        className="w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                                    />
                                                </div>

                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-2">🏷️ Categories</label>
                                                    <div className="flex flex-wrap gap-2">
                                                        {categories.map((cat) => (
                                                            <button
                                                                key={cat.id}
                                                                type="button"
                                                                onClick={() => toggleCategory(idx, cat.id)}
                                                                className={`px-3 py-2 rounded-lg text-sm border transition-all ${
                                                                    job.category_ids.includes(cat.id)
                                                                        ? 'bg-blue-600 text-white border-blue-600 shadow-md'
                                                                        : 'bg-white text-gray-700 border-gray-300 hover:border-blue-300'
                                                                }`}
                                                            >
                                                                {cat.name}
                                                            </button>
                                                        ))}
                                                    </div>
                                                </div>

                                                {/* Custom Taxonomies */}
                                                {Object.entries(customTaxonomies).map(([taxonomy, terms]) => (
                                                    <div key={taxonomy}>
                                                        <label className="block text-sm font-medium text-gray-700 mb-2 capitalize">
                                                            🏷️ {taxonomy.replace('_', ' ')}
                                                        </label>
                                                        <div className="flex flex-wrap gap-2">
                                                            {terms.map((term) => (
                                                                <button
                                                                    key={term.id}
                                                                    type="button"
                                                                    onClick={() => toggleTaxonomy(idx, taxonomy, term.id)}
                                                                    className={`px-3 py-2 rounded-lg text-sm border transition-all ${
                                                                        (job[taxonomy] || []).includes(term.id)
                                                                            ? 'bg-green-600 text-white border-green-600 shadow-md'
                                                                            : 'bg-white text-gray-700 border-gray-300 hover:border-green-300'
                                                                    }`}
                                                                >
                                                                    {term.name}
                                                                </button>
                                                            ))}
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>

                            <div className="mt-6 pt-6 border-t border-gray-200">
                                <PrimaryButton
                                    onClick={postJobs}
                                    disabled={loading || jobs.length === 0}
                                    className="bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                                >
                                    {loading ? (
                                        <>
                                            <LoadingSpinner />
                                            <span>Creating Posts...</span>
                                        </>
                                    ) : (
                                        <>
                                            <span>✨</span>
                                            <span>Create {jobs.length} Draft Post{jobs.length > 1 ? 's' : ''}</span>
                                        </>
                                    )}
                                </PrimaryButton>
                            </div>
                        </div>
                    )}

                    {/* Toast Notification */}
                    {toast && (
                        <Toast
                            type={toast.type}
                            message={toast.message}
                            onClose={() => setToast(null)}
                        />
                    )}
                </div>
            </div>
        </AuthenticatedLayout>
    );
}
