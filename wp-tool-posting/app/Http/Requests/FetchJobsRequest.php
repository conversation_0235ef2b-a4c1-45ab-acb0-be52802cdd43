<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class FetchJobsRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust if you implement authorization logic
    }

    public function rules(): array
    {
        return [
            'urls' => ['required', 'array', 'min:1'],
            'urls.*' => ['required', 'url'],
        ];
    }
}
