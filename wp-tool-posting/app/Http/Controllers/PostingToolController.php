<?php

namespace App\Http\Controllers;

use App\Http\Requests\FetchJobsRequest;
use App\Services\WordPressService;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Inertia\Inertia;

class PostingToolController extends Controller
{
    public function __construct(
        private WordPressService $wordpress, 
        private \App\Services\JobScraper $scraper,
        private \App\Services\OpenAIService $openai
    ) {
    }

    /**
     * Display the main posting tool page.
     */
    public function index()
    {
        $categories = $this->wordpress->getCategories();
        $authors = $this->wordpress->getAuthors();
        $customTaxonomies = $this->wordpress->getCustomTaxonomies();

        return Inertia::render('PostingTool', [
            'categories' => $categories,
            'authors' => $authors,
            'customTaxonomies' => $customTaxonomies,
        ]);
    }

    /**
     * Fetch job details from provided URLs.
     * NOTE: Currently a stub implementation that returns minimal info.
     */
    public function fetch(Request $request)
    {
        $urls = $request->input('urls', []);
        $jobs = [];
        
        // Full Time job type ID is 3
        $fullTimeId = 3;
        
        // Get authors for matching
        $authors = $this->wordpress->getAuthors();
        
        $results = collect($urls)->map(function (string $url) use ($fullTimeId, $authors) {
            $scraped = $this->scraper->scrape($url);
            $content = $scraped['content'] ?? '';
            
            // Extract job details for excerpt
            $jobDetails = $this->openai->extractJobDetails($content);
            
            // Generate excerpt in the required format
            $excerpt = "🔥Hiring 🔥\n\n";
            $excerpt .= "🚀 Company: " . ($jobDetails['company'] ?: '') . "\n\n";
            $excerpt .= "🪛 Position: " . ($jobDetails['position'] ?: '') . "\n\n";
            $excerpt .= "🌎 Location: " . ($jobDetails['location'] ?: 'Remote');
            
            // Only include salary if it exists
            if (!empty($jobDetails['salary'])) {
                $excerpt .= "\n\n💸Salary: " . $jobDetails['salary'];
            }
            
            // Set default job type to Full Time if available
            if ($fullTimeId) {
                $jobType = [$fullTimeId];
            } else {
                $jobType = [];
            }
            
            // Try to determine author based on domain
            $authorId = null;
            $domain = $this->extractDomain($url);
            
            \Illuminate\Support\Facades\Log::info('Author matching - Domain extracted', [
                'url' => $url,
                'domain' => $domain,
                'authors_count' => count($authors)
            ]);
            
            if ($domain) {
                // Extract company name from domain (e.g., automattic.com -> Automattic)
                $companyName = $this->extractCompanyNameFromDomain($domain);
                
                \Illuminate\Support\Facades\Log::info('Author matching - Company name extracted', [
                    'domain' => $domain,
                    'company_name' => $companyName
                ]);
                
                // Try to find matching author
                foreach ($authors as $author) {
                    \Illuminate\Support\Facades\Log::debug('Author matching - Comparing', [
                        'author_id' => $author['id'],
                        'author_name' => $author['name'],
                        'company_name' => $companyName,
                        'match_in_author' => stripos($author['name'], $companyName) !== false,
                        'match_in_company' => stripos($companyName, $author['name']) !== false
                    ]);
                    
                    if (stripos($author['name'], $companyName) !== false || 
                        stripos($companyName, $author['name']) !== false) {
                        $authorId = $author['id'];
                        \Illuminate\Support\Facades\Log::info('Author matching - Found match', [
                            'author_id' => $authorId,
                            'author_name' => $author['name'],
                            'company_name' => $companyName
                        ]);
                        break;
                    }
                }
                
                if ($authorId === null) {
                    \Illuminate\Support\Facades\Log::info('Author matching - No match found', [
                        'company_name' => $companyName,
                        'available_authors' => collect($authors)->pluck('name')->toArray()
                    ]);
                }
            }
            
            return [
                'url' => $url,
                'title' => $scraped['title'] ?? ($url),
                'company' => $jobDetails['company'] ?? null,
                'description' => $content,
                'location' => $jobDetails['location'] ?? 'Remote',
                'excerpt' => $excerpt,
                'job_type' => $jobType, // Add default job type
                'author_id' => $authorId, // Add auto-selected author
            ];
        })->values();

        return response()->json(['jobs' => $results]);
    }

    /**
     * Create draft posts on WordPress.
     */
    public function postJobs(Request $request)
    {
        $jobs = $request->validate([
            'jobs' => 'required|array',
            'jobs.*.title' => 'required|string',
            'jobs.*.content' => 'nullable|string',
            'jobs.*.excerpt' => 'nullable|string',
            'jobs.*.new_application_link' => 'nullable|string',
            'jobs.*.category_ids' => 'array',
            'jobs.*.category_ids.*' => 'integer',
            'jobs.*.job_type' => 'array|nullable',
            'jobs.*.job_type.*' => 'integer',
            'jobs.*.skill' => 'array|nullable',
            'jobs.*.skill.*' => 'integer',
            'jobs.*.preferred_location' => 'array|nullable',
            'jobs.*.preferred_location.*' => 'integer',
            'jobs.*.timezone' => 'array|nullable',
            'jobs.*.timezone.*' => 'integer',
            'jobs.*.salary' => 'array|nullable',
            'jobs.*.salary.*' => 'integer',
            'jobs.*.date' => 'nullable|date',
            'jobs.*.author_id' => 'nullable|integer',
        ])['jobs'];

        $results = collect($jobs)->map(function (array $job) {
            // Clean up the date field - only pass non-empty dates
            $date = Arr::get($job, 'date');
            if (empty($date) || $date === '') {
                $date = null;
            }

            // Prepare custom meta fields
            $meta = [
                'new_application_link' => Arr::get($job, 'new_application_link', Arr::get($job, 'url', ''))
            ];
            
            // Prepare custom taxonomies
            $taxonomies = [
                'job_type' => Arr::get($job, 'job_type', []),
                'skill' => Arr::get($job, 'skill', []),
                'preferred_location' => Arr::get($job, 'preferred_location', []),
                'timezone' => Arr::get($job, 'timezone', []),
                'salary' => Arr::get($job, 'salary', [])
            ];
            
            // Log author ID before creating post
            \Illuminate\Support\Facades\Log::info('Creating post with author', [
                'author_id' => Arr::get($job, 'author_id'),
                'title' => Arr::get($job, 'title')
            ]);
            
            $result = $this->wordpress->createDraftPost(
                Arr::get($job, 'title'),
                Arr::get($job, 'content', ''),
                Arr::get($job, 'category_ids', []),
                $date,
                Arr::get($job, 'author_id'),
                Arr::get($job, 'excerpt'),
                $meta,
                $taxonomies
            );

            return [
                'title' => Arr::get($job, 'title'),
                'success' => $result !== null,
                'post_data' => $result
            ];
        });

        $successful = $results->where('success', true);
        $failed = $results->where('success', false);

        if ($failed->count() > 0) {
            \Illuminate\Support\Facades\Log::warning('Some WordPress posts failed to create', [
                'successful_count' => $successful->count(),
                'failed_count' => $failed->count(),
                'failed_titles' => $failed->pluck('title')->toArray()
            ]);
        }

        return response()->json([
            'created' => $successful->pluck('post_data'),
            'successful_count' => $successful->count(),
            'failed_count' => $failed->count(),
            'message' => $successful->count() > 0
                ? "Successfully created {$successful->count()} draft posts!"
                : 'Failed to create any draft posts. Check logs for details.',
        ]);
    }
    
    /**
     * Extract domain from URL
     *
     * @param string $url
     * @return string|null
     */
    private function extractDomain(string $url): ?string
    {
        $parsedUrl = parse_url($url);
        if (isset($parsedUrl['host'])) {
            return $parsedUrl['host'];
        }
        return null;
    }
    
    /**
     * Extract company name from domain
     * e.g., automattic.com -> Automattic
     *
     * @param string $domain
     * @return string
     */
    private function extractCompanyNameFromDomain(string $domain): string
    {
        // Remove common TLDs and subdomains
        $domain = preg_replace('/^www\./', '', $domain);
        $domain = preg_replace('/\.(com|org|net|io|co|gov|edu|jobs)(\.\w{2})?$/', '', $domain);
        
        // Split by dots and take the first part
        $parts = explode('.', $domain);
        $name = $parts[0];
        
        // Capitalize first letter
        return ucfirst($name);
    }
}
