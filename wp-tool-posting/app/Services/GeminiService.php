<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GeminiService
{
    protected string $apiKey;
    protected string $model;

    public function __construct()
    {
        $this->apiKey = config('services.gemini.key');
        $this->model = config('services.gemini.model', 'gemini-1.5-flash'); // Use a more stable model version
    }

    /**
     * Extract title and content from raw HTML using Gemini.
     * Returns [title, content] array (nulls if failure)
     */
    public function extract(string $html): array
    {
        if (empty($this->apiKey)) {
            Log::error('Gemini API key is not configured');
            return [null, null];
        }

        // Trim very large HTML to reduce token usage
        $htmlSnippet = substr($html, 0, 15000);

        $endpoint = sprintf('https://generativelanguage.googleapis.com/v1beta/models/%s:generateContent?key=%s', $this->model, $this->apiKey);

        $prompt = "You are an expert job posting extractor. Extract and return a JSON object with keys 'title' and 'content' from the following HTML. The 'title' should be the job position title only (e.g. 'Senior Product Designer', not the company name).

For the 'content', carefully analyze the HTML to find the job description section. Look for elements with class names like 'job-description', 'description', 'posting-body', 'job-details', or similar. Also look for content within <article>, <main>, or sections following job title headings. Create a comprehensive plain-text summary (200-400 words) of the job requirements, responsibilities, and qualifications.

If you can't find a specific job description section, look for any content that describes the role, responsibilities, or requirements and summarize that. Do not return 'description missing' unless the page truly contains no job-related information.

ONLY return a valid JSON object with these two keys, nothing else.\n\n" . $htmlSnippet;

        try {
            Log::info('Calling Gemini API', ['model' => $this->model, 'prompt_length' => strlen($prompt)]);
            
            $response = Http::timeout(20)
                ->acceptJson()
                ->post($endpoint, [
                    'contents' => [[
                        'role' => 'user',
                        'parts' => [['text' => $prompt]],
                    ]],
                    'generationConfig' => [
                        'temperature' => 0.2,
                        'maxOutputTokens' => 800,
                    ],
                ]);

            if ($response->failed()) {
                Log::warning('Gemini API request failed', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                return [null, null];
            }

            $data = $response->json();
            
            // Check if the response has the expected structure
            if (!isset($data['candidates'][0]['content']['parts'][0]['text'])) {
                Log::warning('Unexpected Gemini API response structure', ['response' => $data]);
                return [null, null];
            }
            
            $text = $data['candidates'][0]['content']['parts'][0]['text'];
            
            // Try to extract JSON from the response text (even if it's not perfectly formatted)
            $jsonText = $this->extractJsonFromText($text);
            $json = json_decode($jsonText, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::warning('Failed to parse JSON from Gemini response', [
                    'text' => $text,
                    'json_error' => json_last_error_msg()
                ]);
                return [null, null];
            }
            
            $title = $json['title'] ?? null;
            $content = $json['content'] ?? null;
            
            if ($title || $content) {
                Log::info('Gemini extraction successful', [
                    'title' => $title,
                    'content_length' => $content ? strlen($content) : 0
                ]);
                return [$title, $content];
            } else {
                Log::warning('Gemini extraction returned empty title and content');
                return [null, null];
            }
        } catch (\Throwable $e) {
            Log::error('Gemini API error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [null, null];
        }
    }
    
    /**
     * Try to extract JSON from text that might contain additional content
     */
    private function extractJsonFromText(string $text): string
    {
        // Look for JSON object pattern
        if (preg_match('/\{.*\}/s', $text, $matches)) {
            return $matches[0];
        }
        
        return $text;
    }
}
