<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class WordPressService
{
    protected string $baseUrl;
    protected string $username;
    protected string $password;

    public function __construct()
    {
        $this->baseUrl = rtrim(config('services.wordpress.base_url'), '/');
        $this->username = config('services.wordpress.username');
        $this->password = config('services.wordpress.password');
    }

    protected function client()
    {
        return Http::withBasicAuth($this->username, $this->password)
            ->acceptJson();
    }

    /**
     * Fetch all WordPress categories.
     *
     * @return array<int, array<string,mixed>>
     */
    public function getCategories(): array
    {
        $response = $this->client()->get("{$this->baseUrl}/wp-json/wp/v2/categories", [
            'per_page' => 100,
        ]);

        if ($response->failed()) {
            Log::error('Failed to fetch WP categories', ['response' => $response->body()]);
            return [];
        }

        return $response->json();
    }
    
    /**
     * Fetch terms from a custom taxonomy.
     *
     * @param string $taxonomy The taxonomy slug
     * @return array<int, array<string,mixed>>
     */
    public function getTaxonomyTerms(string $taxonomy): array
    {
        $response = $this->client()->get("{$this->baseUrl}/wp-json/wp/v2/{$taxonomy}", [
            'per_page' => 100,
        ]);

        if ($response->failed()) {
            Log::error("Failed to fetch WP taxonomy terms for {$taxonomy}", ['response' => $response->body()]);
            return [];
        }

        return $response->json();
    }
    
    /**
     * Fetch all custom taxonomies.
     *
     * @return array<string, array<int, array<string,mixed>>>
     */
    public function getCustomTaxonomies(): array
    {
        $taxonomies = [
            'job_type',
            'skill',
            'preferred_location',
            'timezone',
            'salary'
        ];
        
        $result = [];
        
        foreach ($taxonomies as $taxonomy) {
            $result[$taxonomy] = $this->getTaxonomyTerms($taxonomy);
        }
        
        return $result;
    }

    /**
     * Fetch all WordPress users who can be authors.
     *
     * @return array<int, array<string,mixed>>
     */
    public function getAuthors(): array
    {
        $allUsers = [];
        $page = 1;
        $perPage = 100;

        do {
            $response = $this->client()->get("{$this->baseUrl}/wp-json/wp/v2/users", [
                'per_page' => $perPage,
                'page' => $page,
                // Remove 'who' parameter to get all users including subscribers
            ]);

            if ($response->failed()) {
                Log::error('Failed to fetch WP users', [
                    'page' => $page,
                    'response' => $response->body()
                ]);
                break;
            }

            $users = $response->json();
            $allUsers = array_merge($allUsers, $users);

            // Check if there are more pages
            $totalPages = (int) $response->header('X-WP-TotalPages', 1);
            $page++;

        } while ($page <= $totalPages && count($users) === $perPage);

        Log::info('Fetched WordPress users', ['total_users' => count($allUsers)]);

        return $allUsers;
    }

    /**
     * Create a draft post in WordPress.
     *
     * @param string $title
     * @param string $content
     * @param array<int> $categoryIds
     * @param string|null $date
     * @param int|null $authorId
     * @param string|null $excerpt
     * @param array<string,mixed> $meta Custom meta fields for the post
     * @param array<string,array<int>> $taxonomies Custom taxonomies for the post
     * @return array<string,mixed>|null
     */
    public function createDraftPost(
        string $title, 
        string $content, 
        array $categoryIds = [], 
        ?string $date = null, 
        ?int $authorId = null, 
        ?string $excerpt = null, 
        array $meta = [],
        array $taxonomies = []
    ): ?array
    {
        $payload = [
            'title'      => $title,
            'content'    => $content,
            'status'     => 'draft',
        ];

        if ($excerpt) {
            $payload['excerpt'] = $excerpt;
        }

        if ($date && !empty(trim($date))) {
            try {
                // Convert to proper WordPress date format
                $dateTime = new \DateTime($date);
                $payload['date'] = $dateTime->format('Y-m-d\TH:i:s');
            } catch (\Exception $e) {
                Log::warning('Invalid date format provided, skipping date field', [
                    'provided_date' => $date,
                    'error' => $e->getMessage()
                ]);
                // Don't include date field if it's invalid
            }
        }

        if (!empty($categoryIds)) {
            $payload['categories'] = $categoryIds;
        }

        if ($authorId) {
            $payload['author'] = $authorId;
        }
        
        // Add custom meta fields if provided
        if (!empty($meta)) {
            $payload['meta'] = $meta;
        }
        
        // Add custom taxonomies if provided
        foreach ($taxonomies as $taxonomy => $termIds) {
            if (!empty($termIds)) {
                $payload[$taxonomy] = $termIds;
            }
        }

        Log::info('Creating WordPress post', [
            'url' => "{$this->baseUrl}/wp-json/wp/v2/posts",
            'payload' => $payload,
            'auth_configured' => !empty($this->username) && !empty($this->password)
        ]);

        $response = $this->client()->post("{$this->baseUrl}/wp-json/wp/v2/posts", $payload);

        if ($response->failed()) {
            Log::error('Failed to create WP post', [
                'status' => $response->status(),
                'response' => $response->body(),
                'headers' => $response->headers()
            ]);
            return null;
        }

        $result = $response->json();
        Log::info('WordPress post created successfully', [
            'post_id' => $result['id'] ?? 'unknown',
            'status' => $result['status'] ?? 'unknown',
            'title' => $result['title']['rendered'] ?? 'unknown'
        ]);

        return $result;
    }
}
