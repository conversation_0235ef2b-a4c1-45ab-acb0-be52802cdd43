<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class JobScraper
{
    public function __construct(private OpenAIService $ai)
    {
    }
    
    /**
     * Scrape basic title and content from a URL.
     * Note: This is a best-effort generic scraper. It can be replaced with
     * site-specific logic later.
     *
     * @return array{title:string|null, content:string|null}
     */
    public function scrape(string $url): array
    {
        try {
            Log::info('Starting job scrape', ['url' => $url]);
            
            // Fetch the URL content
            $response = Http::timeout(15)->get($url);
            if ($response->failed()) {
                Log::warning('Failed to fetch URL', [
                    'url' => $url, 
                    'status' => $response->status()
                ]);
                return ['title' => null, 'content' => null];
            }
            
            $html = $response->body();
            if (empty($html)) {
                Log::warning('Empty HTML response', ['url' => $url]);
                return ['title' => null, 'content' => null];
            }
            
            Log::info('Successfully fetched HTML', [
                'url' => $url,
                'html_length' => strlen($html)
            ]);
            
            // Verify OpenAI API key is set
            $apiKey = config('services.openai.key');
            if (empty($apiKey)) {
                Log::error('OpenAI API key is not set. Check your .env file for OPENAI_API_KEY');
                return ['title' => null, 'content' => null];
            }
            
            // Try OpenAI extraction with web search if possible
            Log::info('Attempting OpenAI extraction with web search');
            try {
                // First try with web search capability
                [$aiTitle, $aiContent] = $this->ai->extractWithWebSearch($html, $url);
                Log::info('Used web search extraction method');
            } catch (\Exception $e) {
                // Fallback to regular extraction if web search fails or is not available
                Log::warning('Web search extraction failed, falling back to regular extraction', ['error' => $e->getMessage()]);
                [$aiTitle, $aiContent] = $this->ai->extract($html);
            }
            
            // Log the extraction results
            if ($aiTitle && $aiContent) {
                Log::info('OpenAI extraction successful', [
                    'title_length' => strlen($aiTitle),
                    'content_length' => strlen($aiContent)
                ]);
                return ['title' => $aiTitle, 'content' => $aiContent];
            } elseif ($aiTitle) {
                Log::warning('OpenAI extracted title but no content', ['title' => $aiTitle]);
                return ['title' => $aiTitle, 'content' => null];
            } elseif ($aiContent) {
                Log::warning('OpenAI extracted content but no title', ['content_length' => strlen($aiContent)]);
                return ['title' => null, 'content' => $aiContent];
            }
            
            Log::warning('OpenAI extraction failed to return title or content');
            return ['title' => null, 'content' => null];
        } catch (\Throwable $e) {
            Log::error('Job scrape failed with exception', [
                'url' => $url, 
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return ['title' => null, 'content' => null];
        }
    }

}
