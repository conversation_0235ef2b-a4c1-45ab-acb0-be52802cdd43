<?php

namespace App\Console\Commands;

use App\Services\WordPressService;
use Illuminate\Console\Command;

class TestWordPressConnection extends Command
{
    protected $signature = 'test:wordpress';
    protected $description = 'Test WordPress API connection and create a test draft post';

    public function handle(WordPressService $wordpress)
    {
        $this->info('Testing WordPress API connection...');
        
        // Test 1: Check configuration
        $baseUrl = config('services.wordpress.base_url');
        $username = config('services.wordpress.username');
        $password = config('services.wordpress.password');
        
        $this->info("Base URL: {$baseUrl}");
        $this->info("Username: " . ($username ? 'configured' : 'NOT CONFIGURED'));
        $this->info("Password: " . ($password ? 'configured' : 'NOT CONFIGURED'));
        $this->newLine();
        
        if (!$baseUrl || !$username || !$password) {
            $this->error('WordPress API credentials are not properly configured in .env file');
            return 1;
        }
        
        // Test 2: Fetch categories
        $this->info('Testing category fetch...');
        $categories = $wordpress->getCategories();
        
        if (empty($categories)) {
            $this->error('Failed to fetch categories. Check your WordPress credentials and API access.');
            return 1;
        }
        
        $this->info('✅ Successfully fetched ' . count($categories) . ' categories');
        $this->table(['ID', 'Name'], collect($categories)->take(5)->map(fn($cat) => [$cat['id'], $cat['name']]));
        $this->newLine();

        // Test 2.5: Fetch authors
        $this->info('Testing authors fetch...');
        $authors = $wordpress->getAuthors();

        if (empty($authors)) {
            $this->warn('⚠️ No authors found or failed to fetch authors');
        } else {
            $this->info('✅ Successfully fetched ' . count($authors) . ' authors');
            $this->table(['ID', 'Name', 'Email'], collect($authors)->take(5)->map(fn($author) => [
                $author['id'],
                $author['name'],
                $author['email'] ?? 'N/A'
            ]));
        }
        $this->newLine();
        
        // Test 3: Create a test draft post
        $this->info('Testing draft post creation...');
        $testTitle = 'Test Post - ' . now()->format('Y-m-d H:i:s');
        $testContent = 'This is a test post created by the WordPress posting tool. You can safely delete this.';
        
        $result = $wordpress->createDraftPost($testTitle, $testContent);
        
        if ($result) {
            $this->info('✅ Successfully created test draft post');
            $this->info("Post ID: {$result['id']}");
            $this->info("Status: {$result['status']}");
            $this->info("Edit URL: {$baseUrl}/wp-admin/post.php?post={$result['id']}&action=edit");
        } else {
            $this->error('❌ Failed to create test draft post');
            return 1;
        }
        
        $this->newLine();
        $this->info('🎉 WordPress connection test completed successfully!');
        return 0;
    }
}
