<?php

namespace App\Console\Commands;

use App\Services\JobScraper;
use Illuminate\Console\Command;

class TestJobScraper extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:job-scraper {url? : The URL to scrape}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the JobScraper service with a URL';

    /**
     * Execute the console command.
     */
    public function handle(JobScraper $scraper)
    {
        $url = $this->argument('url') ?? 'https://automattic.com/work-with-us/job/senior-product-designer/';
        
        $this->info("Testing JobScraper with URL: {$url}");
        $this->info("This may take a few moments...");
        
        $startTime = microtime(true);
        try {
            $result = $scraper->scrape($url);
            $endTime = microtime(true);
        } catch (\Exception $e) {
            $this->error("Exception occurred: " . $e->getMessage());
            $this->line("\nStack trace:\n" . $e->getTraceAsString());
            return 1;
        }
        
        $this->newLine();
        $this->info("Scraping completed in " . round($endTime - $startTime, 2) . " seconds");
        $this->newLine();
        
        if ($result['title'] === null && $result['content'] === null) {
            $this->error("❌ Failed to extract any content from the URL");
            return 1;
        }
        
        $this->info("✅ Extraction Results:");
        $this->newLine();
        
        if ($result['title']) {
            $this->info("📌 Title:");
            $this->line($result['title']);
            $this->newLine();
        } else {
            $this->warn("⚠️ No title was extracted");
            $this->newLine();
        }
        
        if ($result['content']) {
            $this->info("📄 Content:");
            $this->line($result['content']);
            $this->newLine();
            $this->info("Content length: " . strlen($result['content']) . " characters");
        } else {
            $this->warn("⚠️ No content was extracted");
        }
        
        $this->newLine();
        $this->info("Check the logs at storage/logs/laravel.log for more details");
        
        return 0;
    }
}
